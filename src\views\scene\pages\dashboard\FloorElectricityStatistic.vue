<template>
  <div class="w-full h-full flex flex-col">
    <div class="flex-1 flex flex-col bg-[#1E2A47]/10 rounded mt-[0.15vw] p-[0.4vw] dashboard-component-shadow overflow-hidden">
      <!-- 标题和楼层指示 -->
      <div class="text-[0.6vw] dashboard-text-primary mb-[0.4vw] flex justify-between items-center">
        <span>楼层用电统计</span>
        <div class="flex gap-[0.2vw] bg-[#1E2A47]/20 p-[0.2vw] rounded" @mouseenter="pauseAutoSwitch" @mouseleave="resumeAutoSwitch">
          <div
            v-for="floor in floors"
            :key="floor.id"
            class="px-[0.6vw] py-[0.2vw] text-[0.6vw] rounded cursor-pointer transition-colors"
            :class="currentFloor.id === floor.id ? 'bg-[#246CF9] dashboard-text-primary' : 'dashboard-text-primary hover:text-white'"
            @click="handleFloorClick(floor)"
          >
            {{ floor.name }}
          </div>
        </div>
      </div>

      <!-- 左右分栏数据展示 -->
      <div class="flex-1 min-h-0 bg-[#1E2A47]/20 rounded p-[0.4vw] flex gap-[0.4vw]">
        <!-- 左侧：实时数据 -->
        <div class="flex-1 bg-[#1E2A47]/30 rounded p-[0.4vw] flex flex-col">
          <div class="text-[0.5vw] dashboard-text-secondary mb-[0.4vw] text-center">实时数据</div>

          <!-- 设备用电量图表 - 使用横向柱状图展示各设备用电量对比 -->
          <div class="flex-1 bg-[#1E2A47]/30 rounded p-[0.4vw] overflow-hidden">
            <template v-if="loading.real">
              <div class="h-full flex items-center justify-center">
                <div class="pulse-loader"></div>
              </div>
            </template>
            <template v-else-if="realData.devices.length === 0">
              <div class="h-full flex flex-col items-center justify-center text-center">
                <div class="text-[0.4vw] dashboard-text-secondary mb-[0.2vw]"> 暂无设备数据 </div>
                <div class="text-[0.35vw] dashboard-text-secondary"> {{ currentFloor.name }}楼层暂未检测到用电设备 </div>
              </div>
            </template>
            <template v-else>
              <div class="h-full flex flex-col">
                <!-- 图表区域 -->
                <div class="flex-1 relative min-h-0">
                  <svg class="w-full h-full" :viewBox="`0 0 100 ${realData.devices.length * 8}`" preserveAspectRatio="none">
                    <!-- 设备用电量柱状图 -->
                    <g v-for="(device, index) in realData.devices" :key="device.deviceName">
                      <!-- 背景条 -->
                      <rect :x="0" :y="index * 8 + 1" :width="100" :height="6" class="fill-blue-900/20" rx="3" />
                      <!-- 数据条 -->
                      <rect
                        :x="0"
                        :y="index * 8 + 1"
                        :width="chartAnimated ? getDeviceBarWidth(device.valueData) : 0"
                        :height="6"
                        :class="getDeviceBarColor(index)"
                        class="device-bar"
                        rx="3"
                        style="transition: width 1s ease-out"
                      >
                        <title>{{ getDeviceDisplayName(device.deviceName) }}: {{ device.valueData.toLocaleString() }}{{ device.describe }}</title>
                      </rect>
                    </g>
                  </svg>
                  <!-- 设备信息覆盖层 -->
                  <div class="absolute inset-0 flex flex-col justify-around py-[0.1vw] device-info-overlay">
                    <div
                      v-for="(device, index) in realData.devices"
                      :key="device.deviceName"
                      class="flex items-center justify-between px-[0.3vw] h-[1.2vw] device-info-item"
                    >
                      <!-- 左侧：设备名称 -->
                      <div class="flex items-center flex-1 min-w-0">
                        <div class="w-[0.3vw] h-[0.3vw] rounded-sm mr-[0.2vw] flex-shrink-0" :class="getDeviceBarColor(index)"></div>
                        <div class="text-[0.4vw] dashboard-text-primary truncate" :title="device.deviceName">
                          {{ getDeviceDisplayName(device.deviceName) }}
                        </div>
                        <div class="text-[0.35vw] dashboard-text-accent ml-[0.2vw] flex-shrink-0">
                          {{ device.room }}
                        </div>
                      </div>

                      <!-- 右侧：用电量数值 -->
                      <div class="flex items-baseline flex-shrink-0 ml-[0.3vw]">
                        <span class="text-[0.4vw] dashboard-text-primary font-medium">
                          {{ device.valueData.toLocaleString() }}
                        </span>
                        <span class="text-[0.35vw] dashboard-text-secondary ml-[0.1vw]">
                          {{ device.describe }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 最大值参考线 -->
                <div class="text-[0.35vw] dashboard-text-secondary mt-[0.2vw] text-right">
                  最大值: {{ getMaxDeviceValue() }}{{ realData.devices[0]?.describe || 'kW' }}
                </div>
              </div>
            </template>
          </div>
        </div>
        <!-- 右侧：历史数据 -->
        <div class="flex-1 bg-[#1E2A47]/30 rounded p-[0.4vw] flex flex-col">
          <div class="text-[0.5vw] dashboard-text-secondary mb-[0.2vw] text-center">历史用电趋势</div>

          <div class="flex-1 flex flex-col">
            <template v-if="loading.history">
              <div class="flex-1 flex items-center justify-center">
                <div class="pulse-loader"></div>
              </div>
            </template>
            <template v-else>
              <div class="flex-1 relative">
                <!-- 折线图 -->
                <svg class="w-full h-full" viewBox="0 0 240 100" preserveAspectRatio="none">
                  <!-- 水平网格线 -->
                  <line v-for="i in 4" :key="i" x1="0" :y1="i * 20" x2="240" :y2="i * 20" class="stroke-blue-400/10" stroke-width="1" />

                  <!-- 平均值线 -->
                  <line x1="0" :y1="avgLineY" x2="240" :y2="avgLineY" class="stroke-blue-400/50" stroke-width="1" stroke-dasharray="4,4" />

                  <!-- 折线 -->
                  <path :d="getLinePath()" fill="none" class="stroke-blue-400" stroke-width="2" />

                  <!-- 渐变区域 -->
                  <path :d="`${getLinePath()} L 240,100 L 0,100 Z`" class="fill-blue-400/10" />
                </svg>

                <!-- 平均值标签 -->
                <div
                  class="absolute right-[0.4vw] text-[0.4vw] dashboard-text-secondary"
                  :style="{ top: `${avgLineY}%`, transform: 'translateY(-50%)' }"
                >
                  平均: {{ historyData.avgPower }}kW
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue';
  import { getReal, getHistory, type ElectricityHistoryItem } from '/@/api/electricityMeter';
  import { debounce } from 'lodash-es';
  import gsap from 'gsap';

  // 楼层数据
  const floors = [
    { id: 1, name: '1楼', code: 'F1', floorInfo: '1' },
    { id: 2, name: '2楼', code: 'F2', floorInfo: '2' },
    { id: 3, name: '3楼', code: 'F3', floorInfo: '3' },
    { id: 4, name: '4楼', code: 'F4', floorInfo: '4' },
  ];

  // 当前状态
  const currentFloor = ref(floors[0]);
  const loading = reactive({
    real: false,
    history: false,
  });

  // 实时数据类型定义
  interface ElectricityRealItem {
    valueData: number;
    dataTime: string;
    type: string;
    signalName: string;
    describe: string;
    deviceName: string;
    floorInfo: string;
    room: string;
  } // 实时数据
  const realData = reactive({
    devices: [] as ElectricityRealItem[],
  });

  // 图表动画状态
  const chartAnimated = ref(false);

  // 历史数据
  const historyData = reactive({
    hourlyData: Array(24).fill(0),
    maxValue: 100,
    todayTotal: '--',
    peakPower: '--',
    avgPower: '--',
  });

  let floorSwitchTimer: number | null = null;
  let dataRefreshTimer: number | null = null;

  // 自动切换相关
  let autoSwitchPaused = false;

  // 暂停自动切换
  const pauseAutoSwitch = () => {
    autoSwitchPaused = true;
  };

  // 恢复自动切换
  const resumeAutoSwitch = () => {
    autoSwitchPaused = false;
  };
  // 处理手动点击切换楼层 - 使用防抖处理
  const handleFloorClick = debounce((floor) => {
    currentFloor.value = floor;
    fetchAllData();
    pauseAutoSwitch();
    setTimeout(resumeAutoSwitch, 6000); // 暂停一个切换周期
  }, 300); // 获取实时数据
  const fetchRealData = async () => {
    try {
      loading.real = true;
      const data = await getReal(currentFloor.value.floorInfo);

      // 记录原始设备数据
      realData.devices = data;

      // 触发图表动画
      chartAnimated.value = false;
      setTimeout(() => {
        chartAnimated.value = true;
      }, 100);
    } catch (error) {
      console.error('获取实时电表数据失败:', error);
    } finally {
      loading.real = false;
    }
  };

  // 计算平均值线的Y坐标
  const avgLineY = computed(() => {
    if (typeof historyData.avgPower === 'string') return 50;
    const maxValue = historyData.maxValue || 1;
    return 100 - (((historyData.avgPower as number) / maxValue) * 80 + 10);
  });
  // 生成折线路径
  const getLinePath = () => {
    const data = historyData.hourlyData;
    if (!data.length) return '';

    const maxValue = historyData.maxValue || 1;
    const points = data.map((value, index) => {
      const x = (index / 23) * 240;
      const y = 100 - ((value / maxValue) * 80 + 10); // 80%的高度范围，上下留10%间距
      return `${x},${y}`;
    });

    return `M ${points.join(' L ')}`;
  };
  // 获取设备显示名称
  const getDeviceDisplayName = (deviceName: string) => {
    // 提取设备名称的关键部分
    const parts = deviceName.split('｜');
    if (parts.length > 1) {
      // 如果包含｜分隔符，取第二部分（通常是有意义的名称）
      let name = parts[1].split('-').pop() || parts[1];
      // 限制显示长度，避免过长
      if (name.length > 12) {
        name = name.substring(0, 12) + '...';
      }
      return name;
    }
    // 否则取最后的部分
    let name = deviceName.split('-').pop() || deviceName;
    if (name.length > 12) {
      name = name.substring(0, 12) + '...';
    }
    return name;
  };

  // 获取设备用电量的最大值
  const getMaxDeviceValue = () => {
    if (!realData.devices.length) return 0;
    return Math.max(...realData.devices.map((device) => device.valueData)).toLocaleString();
  };

  // 计算设备柱状图宽度百分比
  const getDeviceBarWidth = (value: number) => {
    if (!realData.devices.length) return 0;
    const maxValue = Math.max(...realData.devices.map((device) => device.valueData));
    return maxValue > 0 ? (value / maxValue) * 85 + 5 : 5; // 最小5%，最大90%
  };

  // 获取设备柱状图颜色类
  const getDeviceBarColor = (index: number) => {
    const colors = [
      'fill-blue-400',
      'fill-green-400',
      'fill-yellow-400',
      'fill-purple-400',
      'fill-pink-400',
      'fill-indigo-400',
      'fill-red-400',
      'fill-orange-400',
    ];
    return colors[index % colors.length];
  };

  // 修改历史数据处理
  const processHistoryData = (data: Record<string, ElectricityHistoryItem[]>) => {
    const hourlyData = Array(24).fill(0);
    let maxValue = 0;
    let totalValue = 0;
    let validHours = 0;
    let unit = '';

    // 合并同一楼层的所有设备数据
    Object.values(data).forEach((deviceData) => {
      if (deviceData[0]?.floorInfo === currentFloor.value.floorInfo) {
        deviceData.forEach((item) => {
          const hour = new Date(item.dataTime).getHours();
          const value = item.valueData;

          // 记录单位
          if (!unit && item.describe) {
            unit = item.describe;
          }

          // 更新小时数据
          hourlyData[hour] = (hourlyData[hour] || 0) + value;

          // 更新最大值
          maxValue = Math.max(maxValue, hourlyData[hour]);

          // 累计总值和有效小时数
          totalValue += value;
          if (value > 0) validHours++;
        });
      }
    });

    // 优化设备名称显示
    const deviceNames = new Set<string>();
    Object.values(data).forEach((deviceData) => {
      if (deviceData[0]?.floorInfo === currentFloor.value.floorInfo) {
        const name = deviceData[0]?.deviceName || '';
        // 提取设备名称的最后一部分（通常是最具描述性的部分）
        const shortName = name.split('｜').pop() || name.split('-').pop() || name;
        deviceNames.add(shortName);
      }
    });

    const avgPower = validHours ? totalValue / validHours : 0;
    const formattedDeviceNames = Array.from(deviceNames).join('、');

    return {
      hourlyData,
      maxValue,
      avgPower: avgPower.toFixed(1),
      unit,
      deviceNames: formattedDeviceNames,
    };
  }; // 修改获取历史数据函数
  const fetchHistoryData = async () => {
    try {
      loading.history = true;
      const data = await getHistory(currentFloor.value.floorInfo);
      const processedData = processHistoryData(data);

      // 更新历史数据
      historyData.hourlyData = processedData.hourlyData;
      historyData.maxValue = processedData.maxValue;
      historyData.avgPower = processedData.avgPower;

      // 使用 GSAP 添加数值变化动画
      gsap.to(historyData, {
        duration: 1,
        maxValue: processedData.maxValue,
        ease: 'power2.out',
      });

      // 根据最大值优化Y轴显示
      const roundedMax = Math.ceil(processedData.maxValue / 100) * 100;
      historyData.maxValue = roundedMax;
    } catch (error) {
      console.error('获取历史电表数据失败:', error);
    } finally {
      loading.history = false;
    }
  };

  // 获取所有数据
  const fetchAllData = async () => {
    console.log(`正在获取${currentFloor.value.name}(floorInfo: ${currentFloor.value.floorInfo})的电表数据`);
    await Promise.all([fetchRealData(), fetchHistoryData()]);
  };
  // 自动切换楼层
  const startFloorAutoSwitch = () => {
    let currentIndex = 0;
    floorSwitchTimer = window.setInterval(() => {
      if (autoSwitchPaused) return;
      currentIndex = (currentIndex + 1) % floors.length;
      currentFloor.value = floors[currentIndex];
      fetchAllData();
    }, 6000); // 每6秒切换一次楼层
  };

  // 定期刷新数据
  const startDataRefresh = () => {
    dataRefreshTimer = window.setInterval(() => {
      fetchAllData();
    }, 12000); // 每12秒刷新一次数据
  };

  onMounted(() => {
    // 初始加载数据
    fetchAllData();

    // 启动自动切换和刷新
    startFloorAutoSwitch();
    startDataRefresh();
  });

  onBeforeUnmount(() => {
    // 清理定时器
    if (floorSwitchTimer) {
      clearInterval(floorSwitchTimer);
      floorSwitchTimer = null;
    }
    if (dataRefreshTimer) {
      clearInterval(dataRefreshTimer);
      dataRefreshTimer = null;
    }

    // 清理GSAP动画
    gsap.killTweensOf([realData, historyData]);
  });
</script>

<style scoped>
  .pulse-loader {
    width: 0.8vw;
    height: 0.8vw;
    border-radius: 50%;
    background-color: rgba(96, 165, 250, 0.6);
    animation: pulse 1.5s infinite ease-in-out;
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.6;
    }
    100% {
      transform: scale(0.8);
      opacity: 1;
    }
  }

  .thin-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(96, 165, 250, 0.3) transparent;
  }

  .thin-scrollbar::-webkit-scrollbar {
    width: 0.3vw;
  }

  .thin-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .thin-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(96, 165, 250, 0.3);
    border-radius: 0.15vw;
  }
  .thin-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(96, 165, 250, 0.5);
  }

  /* 图表相关样式 */
  .device-bar {
    transition: all 0.3s ease-in-out;
  }

  .device-bar:hover {
    filter: brightness(1.2);
  }

  .device-info-overlay {
    pointer-events: none;
  }

  .device-info-item {
    transition: background-color 0.2s ease;
  }

  .device-info-item:hover {
    background-color: rgba(30, 42, 71, 0.1);
  }
</style>
