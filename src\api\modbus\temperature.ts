import { defHttp } from '/@/utils/http/axios';

// 接口返回类型定义
interface TemperatureResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    dataValue: number; // 温度/湿度值
    unit: string; // 单位
  };
  timestamp: number;
}

/**
 * 获取温度数据
 */
export function getTemperature() {
  return defHttp.get<TemperatureResponse>({
    url: '/modbus/temperature/getTemperature',
  });
}

/**
 * 获取湿度数据
 */
export function getHumidity() {
  return defHttp.get<TemperatureResponse>({
    url: '/modbus/temperature/getHumidity',
  });
}
