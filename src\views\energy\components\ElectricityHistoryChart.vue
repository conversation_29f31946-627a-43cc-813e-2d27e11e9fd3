<template>
  <a-row>
    <a-col :span="24">
      <div class="relative w-full">
        <div class="flex justify-between items-center mb-4">
          <div class="flex items-center gap-4">
            <span class="text-base">电表历史用量趋势</span>
            <a-select v-model:value="selectedFloor" placeholder="选择楼层" style="width: 120px" size="small">
              <a-select-option v-for="option in floorOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
          <a-button type="primary" size="small" @click="handleRefresh" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>
        <div ref="chartRef" style="width: 100%; height: 50vh"></div>
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch } from 'vue';
  import { debounce } from 'lodash-es';
  import * as echarts from 'echarts';
  import { getElectricityHistory } from '/@/api/energy/electricity';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  const electricityHistoryData = ref<Record<string, any[]>>({});
  const availableMeters = ref<string[]>([]);
  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;

  const loading = ref(false);

  // 楼层选择器相关
  const selectedFloor = ref<string>('all');
  const floorOptions = [
    { label: '全部', value: 'all' },
    { label: '1楼', value: '1' },
    { label: '2楼', value: '2' },
    { label: '3楼', value: '3' },
    { label: '4楼', value: '4' },
  ];

  // 监听楼层变化，重新加载数据
  watch(selectedFloor, () => {
    loadData();
  });

  const renderChart = () => {
    if (!chartInstance || !chartRef.value) return;

    if (availableMeters.value.length === 0) {
      chartInstance.clear();
      chartInstance.setOption({
        title: {
          text: '电表历史用量',
          left: 'center',
          textStyle: {
            color: '#2c3e50',
            fontWeight: 'bold',
            fontSize: 16,
          },
        },
        xAxis: { show: false },
        yAxis: { show: false },
        series: [],
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              style: {
                text: '暂无历史数据',
                fontSize: 16,
                fill: '#999',
              },
            },
          ],
        },
      });
      return;
    }

    const allDates = new Set<string>();
    Object.values(electricityHistoryData.value).forEach((meterData) => {
      meterData.forEach((record) => {
        if (record.dataTime) {
          allDates.add(record.dataTime.split(' ')[0]);
        }
      });
    });

    const sortedDates = Array.from(allDates).sort();

    const series: any[] = [];
    availableMeters.value.forEach((meterName) => {
      if (!electricityHistoryData.value[meterName]) return;

      const meterDataMap = new Map<string, number>();
      electricityHistoryData.value[meterName].forEach((record) => {
        const date = record.dataTime.split(' ')[0];
        const value = parseFloat(record.valueData) || 0;
        meterDataMap.set(date, value);
      });

      const data = sortedDates.map((date) => (meterDataMap.has(date) ? meterDataMap.get(date) : 0));

      series.push({
        name: meterName,
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 7,
        sampling: 'average',
        lineStyle: {
          width: 2.5,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 5,
          shadowOffsetY: 2,
        },
        areaStyle: {
          opacity: 0.3,
        },
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: 3.5,
          },
          itemStyle: {
            borderWidth: 2,
            borderColor: '#fff',
            shadowBlur: 5,
            shadowColor: 'rgba(0,0,0,0.3)',
          },
        },
        label: {
          show: false,
        },
      });
    });

    chartInstance.setOption({
      title: {
        text: '电表历史用量',
        left: 'center',
        textStyle: {
          color: '#2c3e50',
          fontWeight: 'bold',
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: { backgroundColor: '#6a7985' },
          lineStyle: { color: '#555', width: 1, type: 'solid' },
          crossStyle: { color: '#555', width: 1, type: 'solid' },
        },
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' },
        formatter: function (params: any) {
          let res = params[0].name + '<br/>';
          params.forEach(function (item: any) {
            res += item.marker + item.seriesName + ' : ' + item.value + ' kWh<br/>';
          });
          return res;
        },
      },
      legend: {
        type: 'scroll',
        data: availableMeters.value,
        bottom: '6%',
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        itemGap: 15,
        selected: availableMeters.value.reduce(
          (acc, meter) => {
            acc[meter] = true;
            return acc;
          },
          {} as Record<string, boolean>
        ),
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
        backgroundColor: '#f9f9f9',
        borderColor: '#eee',
        show: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: sortedDates,
        axisLabel: {
          interval: 'auto',
          rotate: 0,
          margin: 10,
          formatter: function (value: string) {
            return value;
          },
          hideOverlap: true,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '用电量(kWh)',
        nameTextStyle: {
          padding: [0, 30, 0, 0],
        },
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      series: series,
      color: ['#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622', '#bda29a'], // ECharts default color palette
      dataZoom: [
        {
          type: 'slider',
          xAxisIndex: 0,
          filterMode: 'filter',
          start: 0,
          end: 100,
          bottom: '1%',
          height: 20,
          dataBackground: {
            lineStyle: { color: '#ddd' },
            areaStyle: { color: '#eee' },
          },
          selectedDataBackground: {
            lineStyle: { color: '#c23531' },
            areaStyle: { color: '#c23531', opacity: 0.4 },
          },
          fillerColor: 'rgba(194, 53, 49, 0.2)',
          borderColor: '#ddd',
          handleIcon:
            'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: { color: '#fff', shadowBlur: 3, shadowColor: 'rgba(0, 0, 0, 0.6)', shadowOffsetX: 2, shadowOffsetY: 2 },
          textStyle: { color: '#333' },
        },
        {
          type: 'inside',
          xAxisIndex: 0,
          filterMode: 'filter',
        },
      ],
    });
  };

  const initChart = () => {
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
    }
  };

  const handleResize = debounce(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 300);
  const loadData = debounce(async () => {
    try {
      loading.value = true;
      const floorParam = selectedFloor.value === 'all' ? undefined : selectedFloor.value;
      const result = await getElectricityHistory(floorParam);
      if (result && typeof result === 'object' && Object.keys(result).length > 0) {
        electricityHistoryData.value = result;
        availableMeters.value = Object.keys(result);
        renderChart();
      } else {
        electricityHistoryData.value = {};
        availableMeters.value = [];
        renderChart(); // 调用 renderChart 来显示无数据状态
        if (chartInstance) {
          // 确保 chartInstance 存在
          message.info('暂无电表历史数据');
        }
      }
    } catch (error) {
      console.error('电表历史数据加载错误:', error);
      message.error('加载电表历史数据失败');
      electricityHistoryData.value = {};
      availableMeters.value = [];
      renderChart(); // 调用 renderChart 来显示错误/无数据状态
    } finally {
      loading.value = false;
    }
  }, 500);

  const handleRefresh = () => {
    if (!loading.value) {
      loadData();
    }
  };

  onMounted(() => {
    initChart();
    loadData();
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    window.removeEventListener('resize', handleResize);
  });
</script>
