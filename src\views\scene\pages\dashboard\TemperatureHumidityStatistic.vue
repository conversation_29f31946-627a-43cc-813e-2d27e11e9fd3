<template>
  <div class="w-full h-full relative flex flex-col">
    <div class="flex-1 flex flex-col bg-[#1E2A47]/10 rounded mt-[0.15vw] p-[0.4vw] dashboard-component-shadow">
      <!-- 标题 -->
      <div class="text-[0.6vw] dashboard-text-primary mb-[0.4vw] flex justify-between items-center">
        <span>环境温湿度</span>
        <span v-if="lastUpdateTime" class="text-[0.45vw] dashboard-text-secondary">{{ lastUpdateTime }}</span>
      </div>

      <!-- 温湿度卡片 -->
      <div class="flex-1 grid grid-cols-2 gap-[0.4vw]">
        <!-- 温度卡片 -->
        <div class="flex flex-col bg-[#1E2A47]/20 rounded p-[0.4vw] status-card">
          <div class="flex justify-between items-center">
            <div class="text-[0.5vw] dashboard-text-secondary">实时温度</div>
            <div class="flex items-center">
              <div class="w-[0.5vw] h-[0.5vw] rounded-full" :class="getTemperatureStatusClass()"></div>
            </div>
          </div>
          <div class="flex-1 flex flex-col items-center justify-center">
            <div class="flex items-baseline">
              <div class="text-[1.5vw] font-medium dashboard-text-primary text-shadow-bright">
                <template v-if="loading.temperature">
                  <div class="pulse-loader"></div>
                </template>
                <template v-else>{{ temperature.value }}</template>
              </div>
              <div class="text-[0.6vw] ml-[0.2vw] dashboard-text-secondary">{{ temperature.unit }}</div>
            </div>
            <div class="text-[0.45vw] mt-[0.3vw]" :class="getTemperatureTextClass()">{{ getTemperatureStatus() }}</div>
          </div>
        </div>

        <!-- 湿度卡片 -->
        <div class="flex flex-col bg-[#1E2A47]/20 rounded p-[0.4vw] status-card">
          <div class="flex justify-between items-center">
            <div class="text-[0.5vw] dashboard-text-secondary">相对湿度</div>
            <div class="flex items-center">
              <div class="w-[0.5vw] h-[0.5vw] rounded-full" :class="getHumidityStatusClass()"></div>
            </div>
          </div>
          <div class="flex-1 flex flex-col items-center justify-center">
            <div class="flex items-baseline">
              <div class="text-[1.5vw] font-medium dashboard-text-primary text-shadow-bright">
                <template v-if="loading.humidity">
                  <div class="pulse-loader"></div>
                </template>
                <template v-else>{{ humidity.value }}</template>
              </div>
              <div class="text-[0.6vw] ml-[0.2vw] dashboard-text-secondary">{{ humidity.unit }}</div>
            </div>
            <div class="text-[0.45vw] mt-[0.3vw]" :class="getHumidityTextClass()">{{ getHumidityStatus() }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
  import { getTemperature, getHumidity } from '/@/api/modbus/temperature';
  import { throttle } from 'lodash-es';
  import gsap from 'gsap';

  // 定义状态
  const temperature = reactive({
    value: '--',
    unit: '°C',
  });

  const humidity = reactive({
    value: '--',
    unit: '%',
  });

  const loading = reactive({
    temperature: true,
    humidity: true,
  });

  const lastUpdateTime = ref('');
  let timer: number | null = null;

  // 获取温度数据
  const fetchTemperature = async () => {
    try {
      loading.temperature = true;
      const res = await getTemperature();
      if (res.success) {
        // 使用GSAP动画更新数值
        gsap.to(temperature, {
          value: res.result.dataValue.toFixed(1),
          duration: 1,
          ease: 'power2.out',
        });
        temperature.unit = res.result.unit || '°C';

        updateLastUpdateTime();
      }
    } catch (error) {
      console.error('获取温度数据失败:', error);
    } finally {
      loading.temperature = false;
    }
  };

  // 获取湿度数据
  const fetchHumidity = async () => {
    try {
      loading.humidity = true;
      const res = await getHumidity();
      if (res.success) {
        // 使用GSAP动画更新数值
        gsap.to(humidity, {
          value: res.result.dataValue.toFixed(1),
          duration: 1,
          ease: 'power2.out',
        });
        humidity.unit = res.result.unit || '%';

        updateLastUpdateTime();
      }
    } catch (error) {
      console.error('获取湿度数据失败:', error);
    } finally {
      loading.humidity = false;
    }
  };

  // 更新时间
  const updateLastUpdateTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    lastUpdateTime.value = `更新于 ${hours}:${minutes}:${seconds}`;
  };

  // 获取温度状态
  const getTemperatureStatus = () => {
    const temp = parseFloat(temperature.value as string);
    if (isNaN(temp)) return '等待数据';
    if (temp > 28) return '温度偏高';
    if (temp < 18) return '温度偏低';
    return '温度正常';
  };

  // 获取湿度状态
  const getHumidityStatus = () => {
    const humid = parseFloat(humidity.value as string);
    if (isNaN(humid)) return '等待数据';
    if (humid > 70) return '湿度偏高';
    if (humid < 30) return '湿度偏低';
    return '湿度正常';
  };

  // 获取整体健康状态
  const getOverallHealth = () => {
    const temp = parseFloat(temperature.value as string);
    const humid = parseFloat(humidity.value as string);

    if (isNaN(temp) || isNaN(humid)) return '等待数据';

    // 温度和湿度都在正常范围内
    if (temp >= 18 && temp <= 28 && humid >= 30 && humid <= 70) {
      return '环境良好';
    }

    // 温度或湿度有一项不在正常范围内
    if ((temp < 18 || temp > 28) && humid >= 30 && humid <= 70) {
      return '温度异常';
    }

    if (temp >= 18 && temp <= 28 && (humid < 30 || humid > 70)) {
      return '湿度异常';
    }

    // 温度和湿度都不在正常范围内
    return '环境异常';
  };

  // 获取整体健康状态对应的样式类
  const getOverallHealthClass = () => {
    const status = getOverallHealth();
    if (status === '等待数据') return 'dashboard-text-secondary';
    if (status === '环境良好') return 'dashboard-text-highlight';
    if (status === '温度异常' || status === '湿度异常') return 'dashboard-text-warning';
    return 'dashboard-text-danger';
  };

  // 获取温度状态对应的颜色类
  const getTemperatureStatusClass = () => {
    const temp = parseFloat(temperature.value as string);
    if (isNaN(temp)) return 'bg-gray-300';
    if (temp > 28) return 'bg-red-300';
    if (temp < 18) return 'bg-blue-300';
    return 'bg-green-300';
  };

  // 获取湿度状态对应的颜色类
  const getHumidityStatusClass = () => {
    const humid = parseFloat(humidity.value as string);
    if (isNaN(humid)) return 'bg-gray-300';
    if (humid > 70) return 'bg-red-300';
    if (humid < 30) return 'bg-yellow-300';
    return 'bg-green-300';
  };

  // 获取温度状态对应的文本颜色类
  const getTemperatureTextClass = () => {
    const temp = parseFloat(temperature.value as string);
    if (isNaN(temp)) return 'dashboard-text-secondary';
    if (temp > 28) return 'dashboard-text-danger';
    if (temp < 18) return 'dashboard-text-accent';
    return 'dashboard-text-highlight';
  };

  // 获取湿度状态对应的文本颜色类
  const getHumidityTextClass = () => {
    const humid = parseFloat(humidity.value as string);
    if (isNaN(humid)) return 'dashboard-text-secondary';
    if (humid > 70) return 'dashboard-text-danger';
    if (humid < 30) return 'dashboard-text-warning';
    return 'dashboard-text-highlight';
  };

  // 窗口大小变化时优化性能
  const handleResize = throttle(() => {
    // 只处理窗口大小变化，不再有图表需要调整
  }, 300);

  // 生命周期钩子
  onMounted(() => {
    // 初始加载数据
    fetchTemperature();
    fetchHumidity();

    // 设置定时刷新
    timer = window.setInterval(() => {
      fetchTemperature();
      fetchHumidity();
    }, 30000); // 每30秒刷新一次

    window.addEventListener('resize', handleResize);
  });

  onBeforeUnmount(() => {
    // 清理定时器
    if (timer) {
      clearInterval(timer);
    }

    // 移除事件监听
    window.removeEventListener('resize', handleResize);

    // 清理GSAP动画
    gsap.killTweensOf(temperature);
    gsap.killTweensOf(humidity);
  });
</script>

<style scoped>
  .text-shadow-bright {
    text-shadow: 0 0 12px rgba(255, 255, 255, 0.7);
  }

  .status-card {
    transition:
      transform 0.3s ease,
      background-color 0.3s ease;
  }

  .status-card:hover {
    transform: scale(1.02);
    background-color: rgba(30, 42, 71, 0.4);
  }

  .pulse-loader {
    width: 1vw;
    height: 1vw;
    border-radius: 50%;
    background-color: rgba(96, 165, 250, 0.6);
    animation: pulse 1.5s infinite ease-in-out;
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.6;
    }
    100% {
      transform: scale(0.8);
      opacity: 1;
    }
  }
</style>
