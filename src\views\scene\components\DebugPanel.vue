<template>
  <div class="debug-panel-container" v-if="isVisible">
    <div class="debug-panel-content" v-show="isPanelOpen" ref="panelRef"></div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { DebugGUIManager } from '../lib/debug/DebugGUIManager';

  // 定义属性
  const props = defineProps({
    // 是否可见
    visible: {
      type: Boolean,
      default: false,
    },
    // 场景类型
    sceneType: {
      type: String as () => 'interior' | 'exterior',
      default: 'exterior',
    },
  });

  // 定义事件
  const emit = defineEmits(['update:visible', 'update:sceneType']);

  // 状态
  const isVisible = ref(props.visible);
  const isPanelOpen = ref(false); // 默认不显示调试面板
  const panelRef = ref<HTMLElement | null>(null);
  const debugManager = ref<DebugGUIManager | null>(null);

  // 切换面板显示/隐藏
  const togglePanel = () => {
    isPanelOpen.value = !isPanelOpen.value;
  };

  // 导出切换面板方法供外部使用
  defineExpose({
    togglePanel,
  });

  // 初始化调试面板
  const initDebugPanel = () => {
    if (!panelRef.value) {
      console.warn('面板引用不可用，无法初始化调试面板');
      return;
    }

    try {
      // 获取调试管理器实例并初始化
      debugManager.value = DebugGUIManager.getInstance();
      const guiElement = debugManager.value.init();

      // 直接设置场景类型，这会触发配置加载和应用
      debugManager.value.setSceneType(props.sceneType);

      if (guiElement && panelRef.value) {
        panelRef.value.appendChild(guiElement);
        console.log('调试面板初始化完成，配置已应用');
      }
    } catch (error) {
      console.error('初始化调试面板失败:', error);
      isVisible.value = false;
      emit('update:visible', false);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (event: KeyboardEvent) => {
    // 按下 Ctrl+Shift+D 切换调试面板
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
      isVisible.value = !isVisible.value;
      emit('update:visible', isVisible.value);
      event.preventDefault();
    }

    // 按下 Ctrl+Shift+I 切换室内/室外场景
    if (event.ctrlKey && event.shiftKey && event.key === 'I') {
      const newSceneType = props.sceneType === 'interior' ? 'exterior' : 'interior';
      emit('update:sceneType', newSceneType);

      if (debugManager.value) {
        // 使用调试管理器的方法切换场景类型
        debugManager.value.setSceneType(newSceneType);
      }

      event.preventDefault();
    }
  };

  // 组件挂载时初始化
  onMounted(() => {
    // 移除开发环境检查，改为配置控制
    if (import.meta.env.VITE_ENABLE_DEBUG === 'true') {
      setTimeout(initDebugPanel, 1000);
      window.addEventListener('keydown', handleKeyDown);
    }
  });

  // 组件卸载时清理
  onUnmounted(() => {
    if (debugManager.value) {
      debugManager.value.destroy();
    }

    // 移除键盘事件监听
    window.removeEventListener('keydown', handleKeyDown);
  });
</script>

<style lang="less" scoped>
  .debug-panel-container {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .debug-panel-content {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 10px;
    max-height: 80vh;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }
  }
</style>
